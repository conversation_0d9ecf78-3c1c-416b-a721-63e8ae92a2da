from email.policy import default

from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
import json


class UpdatePlan(models.Model):
    _name = 'th.project.update.plan'
    _description = '<PERSON>ản kế hoạch cập nhật sự án'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'
    _sql_constraints = [('plan_unique_name','UNIQUE(name)','Tên bản ghi đã tồn tại')]

    name = fields.Char('Tên', required=True, tracking=True, help="Tên của bản kế hoạch")
    th_description = fields.Html('Mô tả', help='<PERSON>ô tả nội dung chi tiết của bản kế hoạch')
    state = fields.Selection([('draft', 'Nháp'),
                              ('pending', 'Chờ Duyệt'),
                              ('approved', 'Đã duyệt'),
                              ('done', '<PERSON><PERSON><PERSON> thành'),
                              ('closed', 'Đóng')],
                             string='Trạng thái', default='draft', tracking=True, help="Trạng thái của bản kế hoạch theo tiến trình")
    th_project_ids = fields.Many2many('th.project.project', string='Dự án', tracking=True,help='Gán các dự án liên quan')
    th_task_ids =fields.Many2many('th.project.task', string='Nhiệm vụ', tracking=True, help='Gán các nhiệm vụ liên quan')
    th_task_domain = fields.Json('Domain', compute='_compute_task_domain', help='Domain lọc nhiệm vụ phù hợp với dự án được chọn')
    th_pr_ids = fields.One2many('th.project.pull.request',
                                compute='_compute_pr_ids',
                                 string='Pull Requests',
                                 help='Danh sách Pull Request liên kết với các nhiệm vụ')
    th_type_plan = fields.Selection([('uat', 'update UAT'),
                                     ('product', 'update Production')], string='Loại phiếu kế hoach', required=True)
    th_update_summary = fields.Text(
        string='Cập nhật',
        compute='_compute_update_summary',
        store=True,
        help='Tổng hợp các thẻ tags từ Pull Requests'
    )

    th_count_task = fields.Integer(compute='_compute_count_task', help='Tổng số nhiệm vụ liên kết')
    th_count_pr = fields.Integer(compute='_compute_count_pr', help='Tổng số pull request liên kết')

    @api.depends('th_pr_ids', 'th_pr_ids.th_tags_ids')
    def _compute_update_summary(self):
        for record in self:
            tag_names = record.th_pr_ids.mapped('th_tags_ids.name')
            unique_tags = set(tag_names)
            record.th_update_summary = ','.join(unique_tags)

    #Tự động cập nhật danh sách Pull Request từ các nhiệm vụ liên kết.
    @api.depends('th_task_ids')
    def _compute_pr_ids(self):
        for record in self:
            record.th_pr_ids = record.th_task_ids.mapped('th_pr_ids')

    # Tính tổng nhiệm vụ
    @api.depends('th_task_ids')
    def _compute_count_task(self):
        for record in self:
            record.th_count_task = len(record.mapped('th_task_ids'))
    # Tính tổng pull request
    @api.depends('th_pr_ids')
    def _compute_count_pr(self):
        for record in self:
            record.th_count_pr = len(record.mapped('th_pr_ids'))

    #Tạo domain để lọc nhiệm vụ theo dự án
    @api.depends('th_project_ids')
    def _compute_task_domain(self):

        for record in self:
            if record.th_project_ids:
                record.th_task_domain = json.dumps([('project_id', 'in', record.th_project_ids.ids),('th_deploy_state', '!=', 'product')])
            else:
                record.th_task_domain = json.dumps([('th_deploy_state', '!=', 'product')])

    @api.constrains('th_pr_ids', 'th_task_ids')
    def _check_pr_task_in_plan_tasks(self):
        for plan in self:
            invalid_prs = plan.th_pr_ids.filtered(lambda pr: pr.th_task_id not in plan.th_task_ids)
            if invalid_prs:
                raise ValidationError("Một số Pull Request không thuộc nhiệm vụ trong kế hoạch.")

    #  Mở danh sách nhiệm vụ được gán với bản kế hoạch
    def th_action_view_tasks(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Tasks',
            'view_mode': 'tree,form',
            'res_model': 'th.project.task',
            'domain': [('id', 'in', self.th_task_ids.ids)],
            'context': {'default_project_id': self.th_project_ids.ids},
        }

    # Mở danh sách Pull Request được gán với bản kế hoạch
    def th_action_view_pull_requests(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Pull Requests',
            'view_mode': 'tree,form',
            'res_model': 'th.project.pull.request',
            'domain': [('id', 'in', self.th_pr_ids.ids)],
        }

    # chuyển task sang trạng thái tiếp theo
    def _th_move_tasks_to_next_stage(self, plan_state):
        stage = 'th_is_done_plan' if plan_state == 'done' else 'th_is_close_plan'
        for task in self.th_task_ids:
            project = task.project_id
            move_stage = self.env['th.project.task.type'].search([
                ('th_project_ids', '=', project.id),
                (stage, '=', True)
            ], order='sequence ASC', limit=1)
            if move_stage:
                task.stage_id = move_stage.id


    def _check_all_prs_product(self, plans):
        """Kiểm tra và đóng các kế hoạch nếu tất cả PRs đều ở trạng thái product."""
        for plan in plans:
            if plan.th_pr_ids and all(pr.state == 'product' for pr in plan.th_pr_ids) and plan.state != 'closed':
                plan.write({'state': 'closed'})
                plan._th_move_tasks_to_next_stage('close')
                plan.message_post(
                    body="Kế hoạch tự động chuyển sang trạng thái Đóng vì tất cả Pull Requests đều ở trạng thái Product.")

    def th_done_action(self):
        for record in self:
            record.state = 'done'
            if record.th_pr_ids:
                if record.th_type_plan == 'uat':
                    record.th_task_ids.write({'th_deploy_state': 'uat'})
                    record.th_pr_ids.write({'state': 'uat'})
                    record._th_move_tasks_to_next_stage('done')
                elif record.th_type_plan == 'product':
                    record.th_task_ids.write({'th_deploy_state': 'staging'})
                    record.th_pr_ids.write({'state': 'staging'})
                    record._th_move_tasks_to_next_stage('done')


    def th_close_action(self):
        for record in self:
            record.state = 'closed'
            if record.th_pr_ids and record.th_type_plan == 'product':
                record.th_task_ids.write({'th_deploy_state': 'product'})
                record.th_pr_ids.write({'state': 'product'})
                record._th_move_tasks_to_next_stage('close')
                related_plans = self.env['th.project.update.plan'].search([
                    ('th_task_ids', 'in', record.th_task_ids.ids),
                    ('th_type_plan', '=', 'uat'),
                    ('id', '!=', record.id)
                ])
                if related_plans:
                    self._check_all_prs_product(related_plans)

    def th_pending_action(self):
        for record in self:
            if not record.th_task_ids:
                raise UserError('Phải có nhiệm vụ để chờ duyệt')
            record.state = 'pending'

    def th_approve_action(self):
        for record in self:
            if not record.th_pr_ids:
                raise UserError('Phải có pull request để duyệt')
            record.state = 'approved'
            if record.th_pr_ids:
                if record.th_type_plan == 'uat':
                    record.th_task_ids.write({'th_deploy_state': 'samdev'})
                    record.th_pr_ids.write({'state': 'samdev'})
                elif record.th_type_plan == 'product':
                    record.th_task_ids.write({'th_deploy_state': 'uat'})
                    record.th_pr_ids.write({'state': 'uat'})
                    record._th_move_tasks_to_next_stage('done')


    def th_action_rollback2draft(self):
        for record in self:
            record.state = 'draft'

    @api.model
    def create(self, vals):
        res = super().create(vals)

        task_ids = res.th_task_ids.ids
        if task_ids:
            draft_prs = self.env['th.project.pull.request'].search([
                ('th_task_id', 'in', task_ids),
                ('state', '!=', 'staging'),
            ])
            res.th_pr_ids = draft_prs

        return res

    def write(self, vals):
        res = super().write(vals)
        if 'th_task_ids' in vals:
            for record in self:
                task_ids = record.th_task_ids.ids
                if task_ids:
                    draft_prs = self.env['th.project.pull.request'].search([
                        ('th_task_id', 'in', task_ids),
                        ('state', '!=', 'staging'),
                    ])
                    record.th_pr_ids = draft_prs
                else:
                    record.th_pr_ids = False

        new_state = vals.get('state')
        if new_state:
            for record in self:
                if record.th_task_ids:
                    is_done = new_state in ['done', 'closed']
                    record.th_task_ids.write({'th_is_done': is_done})


        return res
