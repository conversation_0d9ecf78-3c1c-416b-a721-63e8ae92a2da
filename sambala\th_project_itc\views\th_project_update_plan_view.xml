<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="th_project_update_plan_action" model="ir.actions.act_window">
        <field name="name"><PERSON><PERSON><PERSON> <PERSON><PERSON> ho<PERSON></field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.project.update.plan</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="th_project_upadate_plan_view_tree" model="ir.ui.view">
        <field name="name">th_project_update_plan_view_tree</field>
        <field name="model">th.project.update.plan</field>
        <field name="arch" type="xml">
            <tree string="project_upadate_plan_tree">
                <field name="name"/>
                <field name="state"/>
                <field name="th_project_ids" widget="many2many_tags"/>
                <field name="th_task_ids" widget="many2many_tags"/>
                <field name="th_update_summary" optional="hide"/>
                <field name="create_date" string="<PERSON><PERSON><PERSON> tạo"/>
            </tree>
        </field>
    </record>

    <record id="th_project_upadate_plan_view_form" model="ir.ui.view">
        <field name="name">th_project_upadate_plan_view_form</field>
        <field name="model">th.project.update.plan</field>
        <field name="arch" type="xml">
            <form string="project_upadate_plan_form">
                <header>
                        <button name="th_action_rollback2draft" type="object" string="Nháp" states="pending"/>
                        <button name="th_pending_action" type="object" string="Chờ Duyệt" states="draft,approved"/>
                        <button name="th_approve_action" type="object" string="Duyệt" states="pending,done"/>
                        <button name="th_done_action" type="object" string="Hoàn Thành" states="approved,closed"/>
                        <button name="th_close_action" type="object" string="Đóng" attrs="{'invisible': ['|', ('th_type_plan','=','uat'),('state','not in','done')]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,pending,approved,done,closed"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                    <button name="th_action_view_pull_requests"
                                    type="object"
                                    class="oe_stat_button"
                                    icon="fa-archive"
                                    attrs="{'invisible': [('th_count_pr','=',0)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">Pull Request</span>
                                <field name="th_count_pr" string="Pull Request"/>
                            </div>
                        </button>
                        <button name="th_action_view_tasks"
                                    type="object"
                                    class="oe_stat_button"
                                    icon="fa-archive"
                                    attrs="{'invisible': [('th_count_task','=',0)]}">
                        <div class="o_stat_info">
                            <span class="o_stat_text">Nhiệm vụ</span>
                            <field name="th_count_task" string="task"/>
                        </div>
                    </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" attrs="{'readonly': [('state','in',['approved','done','closed'])]}"/>
                            <field name="th_task_domain" invisible="1"/>
                            <field name="th_project_ids" widget="many2many_tags" attrs="{'readonly': [('state','in',['approved','done','closed'])]}"/>
                            <field name="th_task_ids" widget="many2many_tags" domain="th_task_domain"
                                     attrs="{'readonly': [('state','in',['approved','done','closed'])]}"/>
                        </group>
                        <group>
                            <field name="th_type_plan" attrs="{'readonly': [('state','in',['approved','done','closed'])]}"/>
                            <field name="th_update_summary" attrs="{'readonly': [('state','in',['approved','done','closed'])]}"/>
                            <field name="create_date" string="Ngày tạo"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description"/>
                        </page>
                        <page string="Pull Request">
                            <field name="th_pr_ids"
                                   options="{'no_create': True, 'no_edit': True}" attrs="{'readonly': [('state', 'in', ['done','closed'])]}">
                                <tree no_open="1" create="0" edit="0" default_order="th_id_pr_int asc">
                                    <field name="name"/>
                                    <field name="th_link_pr"/>
                                    <field name="th_id_pull_request"/>
                                    <field name="th_id_pr_int" invisible="1"/>
                                    <field name="th_description" optional="hide"/>
                                    <field name="th_task_id" optional="hide"/>
                                    <field name="th_tags_ids" widget="many2many_tags"/>
                                    <field name="th_person_create_id" optional="hide"/>
                                    <field name="state"/>
                                    <field name="create_date" string="Ngày tạo" optional="hide"/>
                                </tree>

                            </field>
                        </page>
                    </notebook>
                </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
            </form>
        </field>
    </record>

    <menuitem id="th_menu_update_plan" name="Bản kế hoạch update" parent="th_menu_project_manager" action="th_project_update_plan_action"/>


</odoo>
