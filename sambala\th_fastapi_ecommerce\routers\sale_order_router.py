from typing import Annotated, List
from ..schemas import  SaleOrderDatas,SambalaOrderUpdate,VnpayResult
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path, Query
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
from odoo.exceptions import UserError
import logging
import json
from datetime import date
router = APIRouter(tags=["EC V1"])
_logger = logging.getLogger(__name__)

def action_job_sale(sale_order, res_partner, product_ids, fastapi):
    sale_order.with_delay().create_lead_account_move(res_partner, product_ids, fastapi)

def log_api(env, state, description, input_data, function_name):
    env['th.log.api'].sudo().create({
        'state': state,
        'th_model': 'sale.order',
        'th_description': description,
        'th_input_data': str(input_data),
        'th_function_call': function_name,
        'is_log_fast_api': True,
    })

@router.post("/api/saleorders")
def create_sale_orders(
        sale_order_data: SaleOrderDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        background_tasks: BackgroundTasks  # Thêm BackgroundTasks làm tham số
):
    """
    API Endpoint: Tạo đơn hàng từ trang E-Commerce.
    """
    try:
        if not fastapi:
            return {"status_code": 400, "message": "FastAPI instance is required"}

        env = fastapi.env
        data = sale_order_data.model_dump(exclude_none=True, exclude_unset=True)
        partner_info = data.pop('partner_info', None)
        order_line_data = data.pop('order_lines', [])

        # Tìm hoặc tạo khách hàng
        res_partner = env['res.partner'].search([('phone', '=', partner_info['phone'])], limit=1) or \
                      env['res.partner'].sudo().create({
                          'name': partner_info['name'],
                          'phone': partner_info['phone'],
                          'email': partner_info.get('email', ''),
                          'th_module_ids': [(4, env.ref('th_setup_parameters.th_apm_module').id)]
                      })
        data['partner_id'] = res_partner.id
        data['th_origin_id'] = env.ref('th_setup_parameters.th_origin_vmc').id if data['ecm_type'] =='ome'else env.ref('th_setup_parameters.th_origin_vstep').id
        data['th_is_sale_vstep'] = True if data['ecm_type'] =='vstep' else False
        # Tạo đơn hàng
        sale_order = env['sale.order'].sudo().with_context(th_sync=True).create(data)

        # Tạo các dòng sản phẩm trong đơn hàng
        product_ids = []
        for order_line in order_line_data:
            order_line['order_id'] = sale_order.id
            if order_line.get('is_reward_line'):
                reward = env['loyalty.reward'].search([('id', '=', order_line['reward_id_sam'])], limit=1)
                if not reward:
                    return {"status_code": 400, "message": f"Reward ID {order_line['reward_id_sam']} không hợp lệ."}
                order_line['product_id'] = reward.discount_line_product_id.id

            ol = env['sale.order.line'].sudo().with_context(th_sync=True).create(order_line)
            product_ids.append(ol.product_id.id)

        # Gọi hàm tạo Lead và xác nhận đơn hàng trong nền (background task)
        background_tasks.add_task(action_job_sale, sale_order, res_partner, product_ids, fastapi)
        fastapi.env['th.log.api'].create({
            'state': 'success',
            'th_model': str(fastapi._name),
            'th_description': "Tạo đơn hàng thành công",
            'is_log_fast_api': True,
            'th_input_data': json.dumps(sale_order_data.model_dump(), ensure_ascii=False),
            'th_fastapi_endpoint_id': fastapi.id,
        })
        return {
            "status_code": 200,
            "message": "Tạo đơn hàng thành công",
        }
    except Exception as e:
        env.cr.rollback()
        print(e)
        fastapi.env['th.log.api'].create({
            'state': 'error',
            'th_model': str(fastapi._name),
            'th_description': f"Lỗi trong quá trình xử lý: {str(e)}",
            'th_input_data': json.dumps(sale_order_data.model_dump(), ensure_ascii=False),
            'th_function_call': str('create_sale_orders'),
            'is_log_fast_api': True,
            'th_fastapi_endpoint_id': fastapi.id,
        })
        return {"status_code": 500, "message": f"Lỗi trong quá trình xử lý: {str(e)}"}

@router.post("/api/order_update")
def update_sambala_order(
        data: SambalaOrderUpdate,
        fastapi=Depends(authenticated_fastapi_endpoint)
):
    env = fastapi.env
    try:
        order = env['sale.order'].sudo().search(
            [('th_order_ecm_id', '=', data.th_order_ecm_id), ('ecm_type', '=', data.ecm_type)], limit=1)
        if not order:
            log_api(env, 'fail', 'Không tìm thấy đơn hàng', data.dict(), 'update_sambala_order')
            return {"status_code": 404, "message": "Không tìm thấy đơn hàng"}

        for rec in order.invoice_ids:
            rec.write({
                'th_payment_method': data.payment_method,
                'th_mail_invoice': data.invoice,
            })
            if data.invoice:
                rec.th_action_send_mail_invoice()

        partner = order.partner_id
        srm_module = env.ref('th_setup_parameters.th_srm_module')
        partner_vals = {
            'email': data.email,
            'phone': data.phone,
            'vat': data.tax,
            'street': data.address,
            'th_company': data.company,
            'name': data.name,
        }

        if srm_module.id in partner.th_module_ids.ids:
            vals_to_write = {}
            for field, value in partner_vals.items():
                if value is None:
                    continue
                if not partner[field]:
                    vals_to_write[field] = value
        else:
            vals_to_write = {k: v for k, v in partner_vals.items() if v is not None}
        partner.sudo().write(vals_to_write)

        log_api(env, 'success', 'Cập nhật thành công', data.dict(), 'update_sambala_order')
        return {"status_code": 200, "message": "Cập nhật thành công"}

    except Exception as e:
        log_api(env, 'error', f"Lỗi khi cập nhật đơn hàng: {str(e)}", data.dict(), 'update_sambala_order')
        return {"status_code": 500, "message": "Lỗi hệ thống khi cập nhật đơn hàng"}

@router.post("/api/vnpay_result")
def vnpay_result(
    data: VnpayResult,
    fastapi=Depends(authenticated_fastapi_endpoint)
):
    env = fastapi.env
    try:
        order = env['sale.order'].sudo().search([('th_order_ecm_id', '=', data.th_order_ecm_id),('ecm_type', '=', data.ecm_type)], limit=1)
        if not order:
            log_api(env, 'fail', 'Không tìm thấy đơn hàng', data.dict(), 'vnpay_result')
            return {"status_code": 404, "message": "Không tìm thấy đơn hàng"}

        if data.stage == 'success':
            account_journal = env['account.journal'].sudo().search([('th_vnpay_auto', '=', True)], limit=1)
            if not account_journal:
                log_api(env, 'fail', 'Không tìm thấy sổ nhật ký cấu hình VNPay', data.dict(), 'vnpay_result')
                return {"status_code": 400, "message": "Không tìm thấy sổ nhật ký cấu hình VNPay"}

            receipt = env['th.receipt.expenditure.book'].sudo().create({
                'th_payment_type': 'inbound',
                'th_account_move_id': order.invoice_ids.id,
                'th_accounting_date': date.today(),
                'th_account_journal_id': account_journal.id,
                'th_amount': data.amount,
                'th_description': data.log,
            })
            log_api(env, 'success', 'Thanh toán thành công', data.dict(), 'vnpay_result')
            return {"status_code": 200, "message": "Thanh toán thành công", "id": receipt.id}

        log_api(env, 'success', "Không thực hiện giao dịch vì stage không phải 'success'", data.dict(), 'vnpay_result')
        return {"status_code": 200, "message": "Không thực hiện giao dịch vì stage không phải 'success'"}
    except Exception as e:
        log_api(env, 'error', f"Lỗi khi xử lý VNPay result: {str(e)}", data.dict(), 'vnpay_result')
        _logger.exception("Lỗi khi xử lý VNPay result: %s", str(e))
        return {"status_code": 500, "message": "Lỗi hệ thống khi xử lý thanh toán"}









