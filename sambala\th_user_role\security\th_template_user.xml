<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="th_category_role_aum_root" model="ir.module.category">
            <field name="name"><PERSON><PERSON> quyền của AUM</field>
            <field name="sequence">99</field>
        </record>
        <!-- ===== USER ===== -->
        <!-- <PERSON><PERSON><PERSON> người dùng -->
        <record id="th_template_type_user" model="res.groups">
            <field name="name">Loại người dùng</field>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- <PERSON><PERSON> hoạch -->
        <record id="th_template_planning_user" model="res.groups">
            <field name="name">Kế hoạch</field>
            <field name="implied_ids" eval="[(4, ref('planning.group_planning_user'))]"/>
        </record>

        <!-- D<PERSON> án -->
        <record id="th_template_project_user" model="res.groups">
            <field name="name"><PERSON><PERSON> án</field>
            <field name="implied_ids" eval="[(4, ref('project.group_project_user'))]"/>
        </record>

        <!-- Thời gian biểu -->
        <record id="th_template_timesheet_user" model="res.groups">
            <field name="name">Thời gian biểu</field>
            <field name="implied_ids" eval="[(4, ref('hr_timesheet.group_hr_timesheet_user'))]"/>
        </record>

        <!-- Hỗ trợ -->
        <record id="th_template_helpdesk_user" model="res.groups">
            <field name="name">Hỗ trợ</field>
            <field name="implied_ids" eval="[(4, ref('helpdesk.group_helpdesk_user'))]"/>
        </record>

        <!-- Kí -->
        <record id="th_template_sign_user" model="res.groups">
            <field name="name">Kí</field>
            <field name="implied_ids" eval="[(4, ref('sign.group_sign_user'))]"/>
        </record>

        <!-- Tài liệu -->
        <record id="th_template_document_user" model="res.groups">
            <field name="name">Tài liệu</field>
            <field name="implied_ids" eval="[(4, ref('documents.group_documents_user'))]"/>
        </record>

        <!-- Chấm công -->
        <record id="th_template_attendances_user" model="res.groups">
            <field name="name">Chấm công</field>
            <field name="implied_ids" eval="[(4, ref('hr_attendance.group_hr_attendance'))]"/>
        </record>

        <!-- Project ITC -->
        <record id="th_template_project_itc_user" model="res.groups">
            <field name="name">Project ITC</field>
            <field name="implied_ids" eval="[(4, ref('th_project_itc.th_group_project_user_customer'))]"/>
        </record>

        <!-- Bán hàng -->
        <record id="th_template_sales_user" model="res.groups">
            <field name="name">Bán hàng</field>
            <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
        </record>

        <!-- ===== MANAGER ===== -->
        <!-- Chấm công (Trưởng phòng) -->
        <record id="th_template_attendances_manager" model="res.groups">
            <field name="name">Chấm công (Trưởng phòng)</field>
            <field name="implied_ids" eval="[(4, ref('hr_attendance.group_hr_attendance_user'))]"/>
        </record>

<!--        <record id="th_category_template_user_root" model="ir.module.category">-->
<!--            <field name="name">Mẫu</field>-->
<!--            <field name="description">Mẫu: Người dùng nội bộ</field>-->
<!--            <field name="sequence">1</field>-->
<!--        </record>-->

<!--        <record id="th_category_template_user" model="ir.module.category">-->
<!--            <field name="name">Mẫu</field>-->
<!--            <field name="description">Mẫu: Người dùng nội bộ</field>-->
<!--            <field name="parent_id" ref="th_category_template_user_root"/>-->
<!--            <field name="sequence">10</field>-->
<!--        </record>-->

        <record id="th_template_user_base" model="res.groups">
            <field name="name">Người dùng nội bộ</field>
<!--            <field name="category_id" ref="th_category_role_aum_root"/>-->
            <field name="th_group_type">aum</field>
            <field name="implied_ids" eval="[
                (4, ref('th_template_type_user')),
                (4, ref('th_template_planning_user')),
                (4, ref('th_template_project_user')),
                (4, ref('th_template_timesheet_user')),
                (4, ref('th_template_helpdesk_user')),
                (4, ref('th_template_sign_user')),
                (4, ref('th_template_document_user')),
                (4, ref('th_template_attendances_user')),
                (4, ref('th_template_project_itc_user')),
                (4, ref('th_template_sales_user'))
            ]"/>
        </record>
</odoo>
