II. <PERSON> tiết chức năng

A. Quản lý phiếu hỗ trợ
    1. Luồng tạo phiếu mới
       - <PERSON><PERSON> thống cho phép người dùng tạo phiếu từ menu phiếu hỗ trợ menu tổng quan hoặc từ menu phiếu. Nếu người dùng tạo phiếu từ menu tổng quan,
         cần phải chọn khu vực và danh mục trước khi nhập thông tin phiếu.
       - Nhập thông tin bắt buộc:
         • Tên phiếu
         • Khu vực và danh mục
         • Đặc điểm danh mục
         • Chủ đề hỗ trợ
       - Sau khi nhập các thông tin trên hệ thống tự động:
         • Gán "helpdesk team" và "phân công cho" (nhân viên theo quy tắc phân công đã thiết lập trong cấu hình "nhóm hỗ trợ").
       - T<PERSON><PERSON> phiếu với trạng thá<PERSON> "<PERSON>ớ<PERSON>" và hệ thống gửi phiếu đến người được phân công (có hiển thị thông báo gửi phiếu trong ghi chú).

    2. Luồng phân công và xử lý (người dùng phải là quản lý hỗ trợ hoặc quản trị viên)
        2.1. Luồng xử lý:
            - Tại phiếu hỗ trợ, người dùng có thể thực hiện các thao tác sau:
                     • Chuyển trạng thái phiếu (Mới, Đang tiến hành, Đã giải quyết, Đang giữ, Nghiệm thu, Đã đóng, Hủy)
                     • Thêm mô tả, những sửa đổi và phương án
                     • Thêm bình luận trao đổi với người dùng
        2.2. Luồng phân công:
            - Tại phiếu hỗ trợ, người dùng có thể click vào nút "Chuyển giao phiếu" để chuyển phiếu cho nhân viên khác hoặc nhóm hỗ trợ khác.:
                     • Người dùng có thể phân công lại phiếu cho nhân viên khác hoặc nhóm hỗ trợ khác.
                     • Hệ thống sẽ tự động cập nhật trạng thái và thông báo cho người được phân công mới.
    3. Các hàm liên quan
        - create()
        - write()
        - button_add_transferring()

B. Cấu hình
    1. Cấu hình khu vực hỗ trợ
        - Tại menu cấu hình, người dùng có thể tạo các khu vực hỗ trợ khác nhau để phân loại phiếu hỗ trợ.
            Người dùng cần nhập các thông tin sau:
               - Tên khu vực hỗ trợ
               - Mô tả
    Các mục duới thao thác tương tự tạo khu vực hỗ trợ
    2. Danh mục hỗ trợ
       - Nhập thông tin tên danh mục
       - Mô tả
       - Khu vực hỗ trợ (ở bước trên)

    3. Chủ đề hỗ trợ
       - Nhập thông tin tên chủ đề
       - Mô tả đặc điểm danh mục

    4. Nhóm hỗ trợ
       - Tạo nhóm hỗ trợ mới bằng cách nhập tên nhóm
       - Gán quản trị viên, nhân viên vào nhóm
       - Tích SLA cho nhóm(nếu cần thiết)

    5. Đặc điểm danh mục
       - Nhập thông tin tên đặc điểm danh mục, danh mục hỗ trợ
       - Tại tab chủ đề chọn thêm chủ để và gán nhóm hỗ trợ

    6. Chính sách SLA
       - Gán SLA cho khu vực/danh mục
       - Cấu hình thời gian xử lý

C. Báo cáo và thống kê (người dùng phải là quản lý hỗ trợ hoặc quản trị viên)
    Tại menu báo cáo, người dùng có thể xem và xuất các báo cáo thống kê liên quan đến phiếu hỗ trợ. Các báo cáo bao gồm:
        1. Phân tích phiếu
           - Số lượng phiếu theo trạng thái
           - Phân công cho
           - Tỷ lệ đúng SLA
           - Đánh giá của người dùng
        2. Phân tích đánh giá
           - Theo nhân viên/nhóm
           - Theo khu vực/danh mục
           - Đánh giá của người dùng
           - So sánh chỉ tiêu
        3. Xuất báo cáo
             - Dạng file Excel
             - Dạng biểu đồ
