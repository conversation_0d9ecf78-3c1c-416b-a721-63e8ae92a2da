Chi tiết chức năng
------------------

A<PERSON> <PERSON><PERSON><PERSON><PERSON> yêu cầu phê duyệt sang module Maintenance
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**1. Luồng hoạt động:**

- Trong view yêu cầu phê duyệt, trong phần thực hiện hiển thị chức năng <PERSON> sang cấp phát tài sản, người dùng có thể
  ấn vào để thực hiện hành động
- <PERSON><PERSON><PERSON> ti<PERSON>, hệ thống kiểm tra yêu cầu cấp phát tài sản, nếu yêu cầu phê duyệt đã có yêu cầu cấp phát tài sản, thì hệ thống
  sẽ đưa ra lỗi ``ValidationError`` và dừng lại
- <PERSON><PERSON> thống kiểm tra xem người dùng hiện tại có phải là một trong những người phê duyệt của yêu cầu không (``approver_ids``).
  Nếu không phải, nó sẽ đưa ra lỗi ``ValidationError``
- Nếu các điều kiện thỏa mãn, hệ thống sẽ tạo một bản ghi mới trong model ``th.asset.allocation`` với các giá trị lấy từ
  phiếu yêu cầu phê duyệt như Người yêu cầu, Danh mục, Lin vực, Mô tả
- Sau khi tạo yêu cầu cấp phát tài sản, trường ``th_asset_requests`` trong bản ghi ApprovalRequest sẽ được cập nhật với ID
  của yêu cầu cấp phát tài sản vừa được tạo
- Cuối cùng, một thông báo sẽ được gửi để thông báo rằng yêu cầu cấp phát tài sản đã được tạo

**2. Các hàm liên quan:**

- ``action_change_maintenance`` (approval.request)
