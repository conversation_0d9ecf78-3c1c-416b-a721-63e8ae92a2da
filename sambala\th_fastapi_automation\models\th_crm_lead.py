from odoo import fields, models, api

class CrmLead(models.Model):
    _inherit = "crm.lead"

    @api.model
    def get_or_create_partner(self, name, phone, email):
        partner = self.env['res.partner'].search([('phone', '=', phone)], limit=1)
        if not partner:
            partner = self.env['res.partner'].create({
                "name": name,
                "phone": str(phone),
                "email": email or '',
            })
        return partner

    @api.model
    def create_crm_lead(self, records, partner):
        status_group = self.env['th.status.category'].search([('name', '=', 'Chưa xử lý')], limit=1)
        status_detail = self.env['th.status.detail'].search([('name', '=', 'Chưa xử lý.CRM')], limit=1)
        channel = self.env['th.info.channel'].search([('name', '=', records.channel)], limit=1)

        values = {
            "partner_id": partner.id,
            "email_from": records.email,
            "phone": records.phone,
            "name": records.name,
            "th_origin_id": records.th_origin_id or False,
            "th_channel_id": channel.id if channel else False,
            "th_dividing_ring_id": records.th_dividing_ring_id or False,
            "th_status_group_id": status_group.id if status_group else False,
            "th_status_detail_id": status_detail.id if status_detail else False,
        }

        if not values.get('th_dividing_ring_id', False) and not values.get('user_id', False):
            values['user_id'] = self.env.user.id
        
        lead = self.sudo().create(values)
        return {"lead_id": [lead.id]}

