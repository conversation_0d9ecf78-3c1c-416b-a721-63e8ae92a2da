import json
import time
from datetime import datetime

import requests
from odoo import api, models, fields, _
from odoo.exceptions import ValidationError, UserError

class ThClipboard(models.Model):
    _inherit = "th.clipboard"

    def action_call_api_sync(self):
        for rec in self:
            rec.sudo().call_api_sync(rec)

    @api.model
    def create(self, values):
        res = super(ThClipboard, self).create(values)
        try:
            for rec in res:
                if rec.th_type_sync == "ontime" and rec.th_model_name == 'th.apm':
                    rec.sudo().schedule_sync_fastapi('th.apm')
                elif rec.th_type_sync == "ontime" and rec.th_model_name != 'th.apm':
                    rec.sudo().call_api_sync(rec)
        except ValidationError as e:
            raise ValidationError(e)
        return res

    @staticmethod
    def sync_fastapi(env, system, endpoint, method, data=None, record_id=None):
        # Bắt đầu đo thời gian chu<PERSON>n bị request
        th_start_prepare = time.perf_counter()

        th_api = env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', system)], limit=1,order='id desc')
        if not th_api:
            raise UserError("No active API server configuration found for 'samb'.")

        headers = {
            "api-key": th_api.th_password or "",
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/api/{endpoint}/{record_id or ''}"

        # Thời gian chuẩn bị request hoàn tất
        th_prepare_time = time.perf_counter() - th_start_prepare

        # Bắt đầu đo thời gian gửi HTTP request
        th_start_request = time.perf_counter()

        try:
            response = requests.request(method=method, url=url, json=data, headers=headers)

            # Thời gian nhận response
            th_request_time = time.perf_counter() - th_start_request
            th_total_time = time.perf_counter() - th_start_prepare

            response.raise_for_status()
            if response.status_code in [200, 201]:
                if response and endpoint =='apmleads':
                    data = json.loads(response.text)
                    record_to_sync = env['th.apm'].browse(record_id)
                    if not record_to_sync.th_partner_id.th_customer_code:
                        record_to_sync.th_partner_id.th_customer_code =data[0]["partner_id"]["th_customer_code"]
                msg = f"Call API thành công  {method}: {th_api.th_api_root}/../{endpoint}/{record_id or ''}"
                type = 'warning'
                status = 'success'
            else:
                msg = f"Call API thất bại {method}: {th_api.th_api_root}/../{endpoint}/{record_id or ''}"
                type = 'danger'
                status = 'error'

            # Ghi log performance metrics
            env['th.sync.performance.log'].create({
                'th_model_name': 'th.apm' if 'apm' in endpoint else 'unknown',
                'th_endpoint': endpoint,
                'th_method': method,
                'th_system_source': 'b2b',
                'th_system_target': system,
                'th_record_id': record_id,
                'th_prepare_time': round(th_prepare_time * 1000, 2),  # Convert to milliseconds
                'th_request_time': round(th_request_time * 1000, 2),
                'th_total_time': round(th_total_time * 1000, 2),
                'th_status': status,
                'th_response_size': len(response.content) if response.content else 0,
                'th_url': url,
                'th_description': msg,
                'th_processing_time': 0 if not response else (
                    response.json().get('processing_time', 0) if isinstance(response.json(), dict) 
                    else (response.json()[-1].get('processing_time', 0) if response.json() and isinstance(response.json(), list) 
                    else 0)
                ) if hasattr(response, 'json') else 0
            })

            env['bus.bus']._sendone(
                env.user.partner_id,
                "simple_notification",
                {
                    "title": "Thông báo",
                    "message": msg,
                    "warning": type,
                    'sticky': False,
                }
            )
            return response.json()
        except requests.HTTPError as e:
            th_request_time = time.perf_counter() - th_start_request
            th_total_time = time.perf_counter() - th_start_prepare

            env['th.sync.performance.log'].create({
                'th_model_name': 'th.apm' if 'apm' in endpoint else 'unknown',
                'th_endpoint': endpoint,
                'th_method': method,
                'th_system_source': 'b2b',
                'th_system_target': system,
                'th_record_id': record_id,
                'th_prepare_time': round(th_prepare_time * 1000, 2),
                'th_request_time': round(th_request_time * 1000, 2),
                'th_total_time': round(th_total_time * 1000, 2),
                'th_status': 'error' or 'timeout',
                'th_url': url,
                'th_description': f"HTTP Error: {str(e)}",
                'th_processing_time': 0 if not response else (
                    response.json().get('processing_time', 0) if isinstance(response.json(), dict) 
                    else (response.json()[-1].get('processing_time', 0) if response.json() and isinstance(response.json(), list) 
                    else 0)
                ) if hasattr(response, 'json') else 0
            })

            try:
                error_content = response.json()
            except ValueError:
                error_content = "No response content"
            raise UserError(f"Failed to sync: {e}, Details: {error_content}")

    def get_config_router(self, model_name):
        model_config = {
            "crm.lead": {
                "endpoint": "crmlead",
                "endpoint_multi": "updatecrm",
                "custom_response": self._process_crm_lead_b2b
            },
            "mail.message": {
                "endpoint": "mailmessage",
                "endpoint_multi": "mailmessages",
            },
            "th.duplicate.lead.notify.wr": {
                "endpoint": "duplicateleadnotify",
                "endpoint_multi": "duplicateleadnotify",
            },
            # Only Mapping ID - data xml
            "crm.stage": {
                "endpoint": "mappingidcrmstage",
            },
            # TransientModel
            "th.care.history": {
                "endpoint": "carehistory",
                "custom_response": self._process_care_history_b2b
            },
            "res.partner": {
                "endpoint": "contact",
                "endpoint_multi": "contact",
            },
            "th.apm": {
                "endpoint": "apmlead",
                "endpoint_multi": "apmleads",
            },
            "th.apm.trait": {
                "endpoint": "thapmtrait",
            },
        }
        return model_config.get(model_name, None)

    def call_api_sync(self, clipboard_data=None):
        def process_record(rec, config):
            """Xử lý chung cho từng bản ghi."""
            try:
                rec.th_status = 'pending'

                response = self.sync_fastapi(
                    self.env,
                    system=rec.th_system,
                    endpoint=config['endpoint'],
                    method=rec.th_method,
                    data=rec.th_data,
                    record_id=rec.th_internal_id
                )

                if response and 'custom_response' in config:
                    config['custom_response'](rec, response)

                rec.write({'th_status': 'success', 'response_data': str(response) if response else 'OK'})
            except Exception as e:
                rec.write({'th_status': 'error', 'response_data': str(e)})

        for rec in clipboard_data:
            config = self.get_config_router(rec.th_model_name)
            if config:
                process_record(rec, config)

    def _process_crm_lead_b2b(self, rec, response):
        """Xử lý logic riêng cho crm.lead."""
        if response.get('crm_lead', None):
            crm_data = response.get('crm_lead')
            cmr_lead = self.env['crm.lead'].browse(rec.th_internal_id)
            if crm_data.get('th_is_a_duplicate_opportunity', False):
                crm_data['th_dup_state'] = 'processing'
            cmr_lead.sudo().with_context(th_sync=True).write(crm_data)
            partner_id = response.get('partner_id')
            if partner_id:
                cmr_lead.partner_id.sudo().with_context(th_sync=True).write(partner_id)

    def _process_care_history_b2b(self, rec, response):
        self.env['th.care.history'].search([('th_crm_lead_new_id', '=', rec.th_internal_id)]).sudo().unlink()
        for line in response:
            care_history = self.env['th.care.history'].create({
                'name': line.get('name'),
                'preview': line.get('preview'),
                'th_crm_lead_new_id': rec.th_internal_id
            })



    def schedule_sync_fastapi(self, th_model_name, th_system='samp'):
        def process_multi_record(data, config):
            try:
                self.th_status = 'pending'

                responses = self.sync_fastapi(
                    self.env,
                    system=th_system,
                    endpoint=config['endpoint_multi'],
                    method='POST',
                    data=data,
                    record_id=rec.th_internal_id
                )
                for record, response in zip(self, responses):
                    if response and 'custom_response' in config:
                        config['custom_response'](record, response)
                    record.write({'th_status': 'success', 'response_data': str(response.get('response')) if response.get('response') else 'OK'})
            except Exception as e:
                self.write({'th_status': 'error', 'response_data': str(e)})

        config = self.get_config_router(th_model_name)
        if config:
            data = []
            for rec in self:
                if th_model_name == 'mail.message' :
                    data.append({
                        'id_b2b': rec.id if rec.id else None,
                        'th_data_mail': rec.th_data,
                    })
                elif th_model_name == 'th.apm':
                    data.append({
                        'id_b2b': rec.th_internal_id,
                        'th_data_apm': rec.th_data,
                    })
                else:
                    data.append({
                        'id_b2b': rec.th_internal_id,
                        'th_data_crm': rec.th_data,
                        'th_data_dup_CRM': rec.th_data,
                        'th_data_res_partner': rec.th_data,
                    })
            process_multi_record(data, config)

    def schedule_sync_fastapi_a_day(self, th_model_name, th_system='samp'):
        """
        Đồng bộ dữ liệu cần xóa từ B2B sang SamP 1 lần/ngày.
        """

        def process_record(record_to_sync, config):
            try:
                # Đặt trạng thái "pending" cho bản ghi trước khi xử lý
                record_to_sync.write({'th_status': 'pending'})

                id_b2b = record_to_sync.th_internal_id
                if not id_b2b:
                    raise ValueError("Không tìm thấy id_b2b trong dữ liệu đồng bộ.")

                # Gửi yêu cầu DELETE đến API FastAPI
                response = self.sync_fastapi(
                    self.env,
                    system=config['th_system'],
                    endpoint=config['endpoint'] + f"/{id_b2b}",  # Chỉ định đúng ID bản ghi trong endpoint
                    method='DELETE',
                    data=None
                )

                # Xử lý phản hồi từ API
                record_to_sync.write({
                    'th_status': response.get('status', 'success'),
                    'response_data': str(response.get('response')) if response.get('response') else 'OK'
                })

            except Exception as e:
                # Xử lý lỗi đồng bộ
                record_to_sync.write({
                    'th_status': 'error',
                    'response_data': str(e)
                })

        # Lấy cấu hình endpoint cho model
        config = self.get_config_router(th_model_name)
        if config:
            config['th_system'] = th_system

            # Lọc một bản ghi cần xóa (th_type_sync = 'a_day' và th_status = 'waiting')
            record_to_sync = self.search([
                ('th_model_name', '=', th_model_name),
                ('th_type_sync', '=', 'a_day'),
                ('th_status', '=', 'waiting')
            ], limit=1)  # Chỉ lấy 1 bản ghi

            if record_to_sync:
                # Gọi hàm xử lý
                process_record(record_to_sync, config)
