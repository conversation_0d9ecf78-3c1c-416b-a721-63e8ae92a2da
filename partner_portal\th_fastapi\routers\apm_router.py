from typing import Annotated, List, Dict, Any
from ..schemas import InternshipConditionsData
from ..schemas import ApmCampaignDatas, APMLeadDatas, ApmTraitDatas, ApmTeamDatas, SaleOrderDatas, RecordDatas, \
    CreateRecordDatas, ApmContactTraitDatas,ApmTraitValueDatas
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path, Query
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
from odoo.exceptions import UserError

router = APIRouter(tags=["apm"])


@router.post("/api/apmcampaign")
def create_apm_campaign(
        apm_campaign_data: ApmCampaignDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            apm_campaign = fastapi.env['th.apm.campaign'].sudo().with_context(th_sync=True).create(
                apm_campaign_data.model_dump(exclude_unset=True,exclude_none=True))
            return {
                'id': apm_campaign.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/apmcampaign/{id}")
def write_apm_campaign(
        apm_campaign_data: ApmCampaignDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_campaign = fastapi.env['th.apm.campaign'].browse(id)
            if not apm_campaign.exists():
                raise HTTPException(status_code=404, detail="APM Campaign not found.")
            data_to_update = apm_campaign_data.model_dump(exclude_unset=True,exclude_none=True)
            apm_campaign.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': apm_campaign.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/apmcampaign/{id}")
def delete_apm_campaign(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_campaign = fastapi.env['th.apm.campaign'].browse(id)
            if not apm_campaign.exists():
                raise HTTPException(status_code=404, detail="APM Campaign not found.")
            apm_campaign.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


th_country_cache = {}
def th_get_country_id(code: str, fastapi: ThFastapi) -> int:
    """Lấy country ID với cache đơn giản"""
    if not code:
        return False

    if code not in th_country_cache:
        country = fastapi.env['res.country'].search([('code', '=', code)], limit=1)
        th_country_cache[code] = country.id if country else False

    return th_country_cache[code]

@router.post("/api/apmleads")
def create_apm_lead(
        records: list[RecordDatas],
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]):
    try:
        if not fastapi:
            raise HTTPException(status_code=401, detail="Authentication required")

        if not records:
            return []

        results = []

        # Phân loại records
        create_records = [r for r in records if r.id_b2b == 0]
        update_records = [r for r in records if r.id_b2b != 0 and r.th_data_apm]

        # Xử lý create records
        if create_records:
            #Lấy partner_info cần tạo
            partner_infos = []
            for record in create_records:
                values = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                partner_info = values.get('partner_info')
                if partner_info and not values.get('th_partner_id'):
                    # Xử lý country_id
                    if partner_info.get('country_id'):
                        country_id = th_get_country_id(partner_info['country_id'], fastapi)
                        if country_id:
                            partner_info['country_id'] = country_id
                            partner_info['th_country_id'] = country_id
                    partner_infos.append(partner_info)

            partners = []
            if partner_infos:
                partners = fastapi.env['res.partner'].create(partner_infos)

            # Tạo APM leads
            partner_idx = 0
            for record in create_records:
                try:
                    values = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                    partner_info = values.pop('partner_info', None)
                    partner_id = False

                    if not values.get('th_partner_id') and partner_info and partner_idx < len(partners):
                        partner_id = partners[partner_idx].id
                        values['th_partner_id'] = partner_id
                        partner_idx += 1

                    apm_lead = fastapi.env['th.apm'].sudo().with_context(th_sync=True).th_create_th_apm(values)

                    results.append({
                        "status": "success",
                        "response": 'ok',
                        "id": apm_lead.id,
                        'th_partner_id': partner_id,
                    })
                except Exception as e:
                    results.append({
                        "status": "error",
                        "response": str(e),
                        "id": None,
                        'th_partner_id': None,
                    })

        # Xử lý update records
        if update_records:
            # xử lý hàng loạt
            apm_ids = [r.id_b2b for r in update_records]
            apm_records = fastapi.env['th.apm'].browse(apm_ids)
            apm_dict = {apm.id: apm for apm in apm_records if apm.exists()}

            for record in update_records:
                try:
                    apm_lead = apm_dict.get(record.id_b2b)
                    if not apm_lead:
                        results.append({
                            "status": "error",
                            "response": "Không tìm thấy cơ hội",
                            "id": record.id_b2b,
                        })
                        continue

                    data_to_update = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                    apm_lead.sudo().with_context(th_sync=True).th_write_th_apm(data_to_update)

                    results.append({
                        "status": "success",
                        "response": "updated",
                        "id": record.id_b2b,
                    })
                except Exception as e:
                    results.append({
                        "status": "error",
                        "response": str(e),
                        "id": record.id_b2b,
                    })

        return results

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


# @router.post("/api/apmleads")
# def create_apm_lead(
#         records: list[RecordDatas],
#         fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]):
#     try:
#         if fastapi:
#             results = []
#             for record in records:
#                 if record.id_b2b == 0:
#                     values = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
#                     partner_info = values.pop('partner_info', None)
#                     partner_id = False
#                     if not values.get('th_partner_id') and partner_info:
#                         # if partner_info['phone']:
#                         #     partner_old =  fastapi.env['res.partner'].search([('phone', '=', partner_info['phone'])],limit=1)
#                         #     if partner_old:
#                         #         values['th_partner_id'] = partner_old.id
#                         if partner_info['country_id']:
#                             country = fastapi.env['res.country'].search([('code', '=', partner_info['country_id'])],
#                                                                         limit=1)
#                             partner_info['country_id'] = country.id
#                             partner_info['th_country_id'] = country.id
#                         partner_id = fastapi.env['res.partner'].create(partner_info).id
#                         values['th_partner_id'] = partner_id if partner_id else False
#                         # results.append({
#                         #     "th_partner_id": partner.id,
#                         # })
#                     apm_lead = fastapi.env['th.apm'].sudo().with_context(th_sync=True).th_create_th_apm(values)
#                     results.append({
#                         "status": "success",
#                         "response": 'ok',
#                         "id": apm_lead.id,
#                         'th_partner_id': partner_id,
#                     })
#                 elif record.id_b2b != 0 and record.th_data_apm:
#                     response = update_apm_lead(record.th_data_apm, fastapi, record.id_b2b)
#                     results.append({
#                         "status": "success",
#                         "response": response,
#                         "id": record.id_b2b,
#                     })
#                 elif record.id_b2b != 0 and not record.th_data_apm:
#                     response = delete_apm_lead(fastapi, record.id_b2b)
#                     results.append({
#                         "status": "success",
#                         "response": response,
#                     })
#             return results
#     except UserError as e:
#         raise HTTPException(status_code=400, detail=str(e))


# @router.put("/api/ApmLead/{id}")
def update_apm_lead(
        ApmLeadDatas: APMLeadDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            th_apm = fastapi.env['th.apm'].browse(id)
            if not th_apm.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy cơ hội.")
            data_to_update = ApmLeadDatas.model_dump(exclude_none=True, exclude_unset=True)
            th_apm.sudo().with_context(th_sync=True).th_write_th_apm(data_to_update)
            return

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


# @router.delete("/api/ApmLead/{id}")
def delete_apm_lead(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_leads = fastapi.env['th.apm'].browse(id)
            if not apm_leads:
                raise HTTPException(status_code=404, detail="Không tìm thấy các cơ hội.")

            for apm_lead in apm_leads:
                if apm_lead.th_partner_id and apm_lead.th_type_of_care == 'advise':
                    raise HTTPException(
                        status_code=403,
                        detail=f"Không thể xóa cơ hội {apm_lead.id} vì đã được bàn giao cho tư vấn chăm sóc."
                    )

            apm_leads.sudo().with_context(th_sync=True).unlink()
            return {"status": "success", "message": "Xóa thành công các bản ghi APM."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))
@router.post("/api/thapmtraitvalue")
def create_apm_trait_value(
        apm_trait_value_data: ApmTraitValueDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    """Tạo bản ghi mới trong 'th.apm.trait.value'."""
    try:
        if fastapi:
            apm_trait_value = fastapi.env['th.apm.trait.value'].create(
                apm_trait_value_data.model_dump(exclude_unset=True, exclude_none=True)
            )
            return {'id': apm_trait_value.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/api/thapmtraitvalue/{id}")
def write_apm_trait_value(
        apm_trait_value_data: ApmTraitValueDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    """Cập nhật bản ghi theo ID."""
    try:
        if fastapi:
            apm_trait_value = fastapi.env['th.apm.trait.value'].browse(id)
            if not apm_trait_value.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            apm_trait_value.sudo().write(
                apm_trait_value_data.model_dump(exclude_unset=True, exclude_none=True)
            )
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/thapmtraitvalue/{id}")
def delete_apm_trait_value(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    """Xóa bản ghi theo ID."""
    try:
        if fastapi:
            apm_trait = fastapi.env['th.apm.trait.value'].browse(id)
            if not apm_trait.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            apm_trait.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/api/thapmtrait")
def create_apm_trait(
        apm_trait_data: ApmTraitDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            apm_trait = fastapi.env['th.apm.trait'].create(apm_trait_data.model_dump(exclude_unset=True,exclude_none=True))
            return {
                'id': apm_trait.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/thapmtrait/{id}")
def write_apm_trait(
        apm_trait_data: ApmTraitDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_trait = fastapi.env['th.apm.trait'].browse(id)
            if not apm_trait.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            apm_trait_datas = apm_trait_data.model_dump(exclude_unset=True,exclude_none=True)
            # data_for_apm_trait = {
            #     'name': apm_trait_data.name if apm_trait_data.name else apm_trait.name,
            #     'th_origin_id': apm_trait_data.th_origin_id if apm_trait_data.th_origin_id else apm_trait.th_origin_id.id,
            # }
            apm_trait.sudo().write(apm_trait_datas)
            # apm_trait.th_apm_trait_value_ids.unlink()
            # for rec in apm_trait_data.th_apm_trait_value_ids:
            #     apm_trait_value = fastapi.env['th.apm.trait.value'].with_context(
            #         default_th_apm_trait_id=apm_trait.id).create(rec.dict())
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/thapmtrait/{id}")
def delete_apm_trait(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_trait = fastapi.env['th.apm.trait'].browse(id)
            if not apm_trait.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            apm_trait.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/thapmcontacttrait")
def create_apm_contact_trait(
        apm_contact_trait_data: ApmContactTraitDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            data_for_trait = {
                'th_origin_id': apm_contact_trait_data.th_origin_id,
                'th_apm_trait_id': apm_contact_trait_data.th_apm_trait_id,
                'th_apm_trait_value_ids': apm_contact_trait_data.th_apm_trait_value_ids,
            }
            apm_trait = fastapi.env['th.apm.contact.trait'].create(data_for_trait)
            return {
                'id': apm_trait.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/thapmcontacttrait/{id}")
def write_apm_trait(
        apm_trait_data: ApmContactTraitDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_contact_trait = fastapi.env['th.apm.contact.trait'].browse(id)
            if not apm_contact_trait.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            data_to_update = apm_trait_data.model_dump(exclude_unset=True,exclude_none=True)
            apm_contact_trait.sudo().write(data_to_update)
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/thapmcontacttrait/{id}")
def delete_apm_trait(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_contact_trait = fastapi.env['th.apm.contact.trait'].browse(id)
            if not apm_contact_trait.exists():
                raise HTTPException(status_code=404, detail="APM Contact Trait not found.")
            apm_contact_trait.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/apmteam")
def create_apm_team(
        ApmTeamDatas: ApmTeamDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            apm_team = fastapi.env['th.apm.team'].th_create_apm_team(datas=ApmTeamDatas)
            return {'id': apm_team.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/apmteam/{id}")
def write_apm_team(
        ApmTeamDatas: ApmTeamDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_team = fastapi.env['th.apm.team'].browse(id)
            if not apm_team.exists():
                raise HTTPException(status_code=404, detail="Team not found.")
            data_to_update = ApmTeamDatas.model_dump(exclude_unset=True,exclude_none=True)
            apm_team.write(data_to_update)
            return {
                'id': apm_team.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/apmteam/{id}")
def delete_apm_team(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_team = fastapi.env['th.apm.team'].browse(id)
            if not apm_team.exists():
                raise HTTPException(status_code=404, detail="Exempted Subject not found.")

            apm_team.unlink()
            return {"detail": f"Exempted Subject with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/saleorders")
def create_sale_orders(
        sale_order_data: list[CreateRecordDatas],
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            responses = []
            for rec in sale_order_data:
                values = rec.model_dump()
                if values.get('id_b2b', False):
                    response = write_sale_order(rec.th_data_sale, fastapi, values['id_b2b'])
                    responses.append({
                        'id': values.get('id_b2b', False),
                    })
                else:
                    th_data_sale = values.get('th_data_sale', {})
                    th_srm_lead_id = th_data_sale.get('th_srm_lead_id')
                    if th_srm_lead_id:
                        srm = fastapi.env['th.student'].search([('id', '=', th_srm_lead_id)], limit=1)
                        if srm and srm.th_partner_id:
                            th_data_sale['partner_id'] = srm.th_partner_id.id
                            th_data_sale['partner_invoice_id'] = srm.th_partner_id.id
                            th_data_sale['partner_shipping_id'] = srm.th_partner_id.id
                    elif th_data_sale.get('th_apm_id'):
                        th_apm_id = th_data_sale['th_apm_id']
                        apm = fastapi.env['th.apm'].search([('id', '=', th_apm_id)], limit=1)
                        if apm and apm.th_partner_id:
                            th_data_sale['partner_id'] = apm.th_partner_id.id
                            th_data_sale['partner_invoice_id'] = apm.th_partner_id.id
                            th_data_sale['partner_shipping_id'] = apm.th_partner_id.id
                    order_line_data = values['th_data_sale']['order_lines']
                    del values['th_data_sale']['order_lines']
                    sale_order = fastapi.env['sale.order'].sudo().with_context(th_sync=True).create(
                        values['th_data_sale'])
                    line_ids = []
                    if order_line_data:
                        for order_line in order_line_data:
                            order_line['order_id'] = sale_order.id
                            if order_line['product_template_id'] and order_line['product_id']:
                                ol = fastapi.env['sale.order.line'].sudo().with_context(th_sync=True).create(order_line)
                            elif order_line['product_template_id'] and not order_line['product_id']:
                                product = fastapi.env['product.product'].search([('product_tmpl_id','=',order_line['product_template_id'])])
                                if product:
                                    order_line['product_id'] = product.id
                                    ol = fastapi.env['sale.order.line'].sudo().with_context(th_sync=True).create(order_line)
                            line_ids.append(ol.id)

                    responses.append({
                        'id': sale_order.id,
                        'line_ids': line_ids
                    })

            return responses
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def write_sale_order(
        sale_order_data: SaleOrderDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            sale_order = fastapi.env['sale.order'].browse(id)
            if not sale_order.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy đơn hàng.")
            data_to_update = sale_order_data.model_dump(exclude_none=True, exclude_unset=True)
            if 'order_lines' in data_to_update.keys():
                order_line_data = data_to_update.pop('order_lines')
                line_ids = []
                if sale_order.th_status_payment_invoiced != 'paid':
                    if order_line_data:
                        sale_order.order_line.unlink()
                        for order_line in order_line_data:
                            order_line['order_id'] = sale_order.id
                            ol = fastapi.env['sale.order.line'].sudo().with_context(th_sync=True).create(order_line)
                            line_ids.append(ol.id)
            sale_order.sudo().with_context(th_sync=True).write(data_to_update)
            # return {
            #         'id': sale_order.id,
            #         'line_ids': line_ids if line_ids else False
            # }

            # if line_ids:
            #     return {
            #         'line_ids': line_ids
            #     }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/saleorder/{id}")
def delete_sale_order(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            sale_order = fastapi.env['sale.order'].browse(id)
            if not sale_order.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy đơn hàng.")
            sale_order.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))
