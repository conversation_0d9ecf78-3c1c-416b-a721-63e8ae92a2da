from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import timedelta

class ThSyncPerformanceLog(models.Model):
    _name = 'th.sync.performance.log'
    _description = 'Log hiệu suất đồng bộ dữ liệu'
    _order = 'create_date desc'
    _rec_name = 'th_endpoint'

    th_model_name = fields.Char('Model', required=True, index=True)
    th_endpoint = fields.Char('API Endpoint', required=True, index=True)
    th_method = fields.Selection([('GET', 'GET'),('POST', 'POST'),('PUT', 'PUT'),('DELETE', 'DELETE')], string='HTTP Method', required=True, index=True)
    th_system_source = fields.Selection([('b2b', 'B2B Portal'),('samp', 'SamP System'),('b2c', 'B2C System')], string='Hệ thống gửi', required=True, index=True)
    th_system_target = fields.Selection([
        ('b2b', 'B2B Portal'),
        ('samp', 'SamP System'),
        ('b2c', 'B2C System')
    ], string='Hệ thống nhận', required=True, index=True)
    th_record_id = fields.Integer('Record ID', index=True)
    th_url = fields.Text('URL đầy đủ')
    th_prepare_time = fields.Float('Thời gian chuẩn bị (ms)', digits=(10, 2), help="Thời gian chuẩn bị request")
    th_request_time = fields.Float('Thời gian HTTP request (ms)', digits=(10, 2), help="Thời gian gửi và nhận HTTP response")
    th_processing_time = fields.Float('Thời gian xử lý tại đích (ms)', digits=(10, 2), help="Thời gian xử lý tại hệ thống đích")
    th_total_time = fields.Float('Tổng thời gian (ms)', digits=(10, 2), help="Tổng thời gian end-to-end")
    th_status = fields.Selection([('success', 'Thành công'),('error', 'Lỗi'),('timeout', 'Timeout'),('retry', 'Thử lại')], string='Trạng thái', required=True, index=True)
    th_response_size = fields.Integer('Kích thước response (bytes)')
    th_description = fields.Text('Mô tả')
    th_error_details = fields.Text('Chi tiết lỗi')
    th_is_slow = fields.Boolean('Chậm', compute='_compute_performance_flags', store=True)
    th_is_very_slow = fields.Boolean('Rất chậm', compute='_compute_performance_flags', store=True)
    th_performance_level = fields.Selection([
        ('excellent', 'Xuất sắc (< 100ms)'),
        ('good', 'Tốt (100-500ms)'),
        ('average', 'Trung bình (500ms-1s)'),
        ('slow', 'Chậm (1-3s)'),
        ('very_slow', 'Rất chậm (> 3s)')
    ], string='Mức hiệu suất', compute='_compute_performance_flags', store=True)
    th_sync_date = fields.Date('Ngày đồng bộ', default=fields.Date.context_today, index=True)
    th_sync_hour = fields.Integer('Giờ đồng bộ', compute='_compute_sync_hour', store=True)

    @api.depends('create_date')
    def _compute_sync_hour(self):
        for record in self:
            if record.create_date:
                record.th_sync_hour = record.create_date.hour
            else:
                record.th_sync_hour = 0

    @api.depends('th_total_time')
    def _compute_performance_flags(self):
        for record in self:
            total_time = record.th_total_time or 0
            # Phân loại hiệu suất
            if total_time < 100:
                record.th_performance_level = 'excellent'
                record.th_is_slow = False
                record.th_is_very_slow = False
            elif total_time < 500:
                record.th_performance_level = 'good'
                record.th_is_slow = False
                record.th_is_very_slow = False
            elif total_time < 1000:
                record.th_performance_level = 'average'
                record.th_is_slow = False
                record.th_is_very_slow = False
            elif total_time < 3000:
                record.th_performance_level = 'slow'
                record.th_is_slow = True
                record.th_is_very_slow = False
            else:
                record.th_performance_level = 'very_slow'
                record.th_is_slow = True
                record.th_is_very_slow = True

    @api.model
    def th_cleanup_old_logs(self, days_to_keep=30):
        """Dọn dẹp log cũ"""
        cutoff_date = fields.Date.context_today(self) - timedelta(days=days_to_keep)
        old_logs = self.search([('th_sync_date', '<', cutoff_date)])
        old_logs.unlink()
        return len(old_logs)
