<odoo>
    <record id="th_helpdesk_tickets_view_tree" model="ir.ui.view">
        <field name="name">th_helpdesk_tickets_view_tree</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_tree"/>
        <field name="arch" type="xml">

            <xpath expr="//tree" position="attributes">
                <attribute name="decoration-danger">x_ticket_rating=='4' or x_ticket_rating=='5'</attribute>
                <attribute name="decoration-bf">th_ticket_stage=='solved'</attribute>
                <attribute name="default_order"></attribute>
            </xpath>
            <xpath expr="//field[@name='ticket_ref']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="widget"></attribute>
            </xpath>
            <xpath expr="//field[@name='activity_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='sla_deadline']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='stage_id']" position="after">
                <field name="priority" position="move"/>
                <field name="x_ticket_rating"/>
                <field name="create_uid"/>
                <field name="th_ticket_stage" invisible="1"/>
                <field name="th_type_errol" optional="hide"/>
                <field name="th_composite" optional="hide"/>
                <field name="th_major_id" optional="hide"/>
                <field name="th_user_production_id" optional="hide"/>
                <field name="write_uid" string="Người cập nhật lần cuối" optional="show"/>
            </xpath>

        </field>
    </record>

    <record id="th_helpdesk_ticket_view_form" model="ir.ui.view">
        <field name="name">th_helpdesk_ticket_view_form</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority">80</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_form"/>
        <field name="arch" type="xml">

            <!--            <xpath expr="//button[@name='action_timer_start'][1]" position="attributes">-->
            <!--                <attribute name="invisible">1</attribute>-->
            <!--            </xpath>-->
            <!--            <xpath expr="//button[@name='action_timer_start'][2]" position="attributes">-->
            <!--                <attribute name="invisible">1</attribute>-->
            <!--            </xpath>-->
            <!--            <xpath expr="//button[@name='action_timer_stop']" position="attributes">-->
            <!--                <attribute name="invisible">1</attribute>-->
            <!--            </xpath>-->
            <!--            <xpath expr="//button[@name='action_timer_pause']" position="attributes">-->
            <!--                <attribute name="invisible">1</attribute>-->
            <!--            </xpath>-->
            <!--            <xpath expr="//button[@name='action_timer_resume']" position="attributes">-->
            <!--                <attribute name="invisible">1</attribute>-->
            <!--            </xpath>-->

<!--            <xpath expr="//field[@name='stage_id']" position="attributes">-->
<!--                <attribute name="attrs">{'readonly': [('th_check_helpdesk_normal_user', '=', True)]}</attribute>-->
<!--            </xpath>-->
            <xpath expr="//button[@name='assign_ticket_to_self']" position="after">
                <field name="th_requests_id" invisible="1"/>
                <field name="th_ticket_stage" invisible="1"/>
                <field name="th_is_maintenance" invisible="1"/>
                <field name="th_is_user_ticket" invisible="1"/>
                <field name="x_rating_ids" invisible="1"/>
                <field name="th_is_deadline_assignees" invisible="1"/>
                <field name="th_is_deadline_user" invisible="1"/>
                <button name="button_add_rating" string="Add a rating" type="object" class="oe_highlight"
                        attrs="{'invisible': ['|', ('x_is_ticket_seeker', '=', False), ('th_ticket_stage', '!=', 'test')]}"/>
                <button name="button_add_transferring" string="Add a transfer" type="object" class="oe_highlight" groups= "th_helpdesk.group_helpdesk_care_manager"
                        attrs="{'invisible': ['|', ('th_is_deadline_assignees', '=', False), ('th_ticket_stage', 'not in', ['new', 'in_progress'])]}"
                        />
            </xpath>
            <xpath expr="//button[@name='assign_ticket_to_self']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//div[@name='button_box']" position="replace">
                <div class="oe_button_box" name="button_box">
                    <field name="x_is_ticket_seeker" invisible="1"/>
                    <field name="x_rating_ids" invisible="1"/>
                    <button class="oe_stat_button" name="action_view_ticket_rating" string="Ticket rating" type="object"
                            icon="fa-ticket" attrs="{'invisible': [('x_rating_ids', '=', [])]}"/>
                </div>
            </xpath>

            <xpath expr="//div[hasclass('oe_title')]/field[@name='sla_status_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[hasclass('oe_title')]/span" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='team_id']" position="before">
                <field name="partner_id" position="move"/>
                <xpath expr="//label[@for='partner_email']" position="move"/>
                <xpath expr="//group//group[2]//div[hasclass('o_row_readonly')]" position="move"/>
                <xpath expr="//label[@for='partner_phone']" position="move"/>
                <xpath expr="//group//group[2]//div[hasclass('o_row_readonly')]" position="move"/>
                <field name="th_is_deadline_user" invisible="1"/>
                <field name="th_ticket_stage" invisible="1"/>
                <field name="th_deadline_user"/>
                <field name="create_uid"/>
                <field name="th_attachment_ids" widget="many2many_binary"/>
            </xpath>
            <xpath expr="//field[@name='email_cc']" position="before">
                <field name="th_user_id_domain" invisible="1"/>
                <field name="th_check_helpdesk_normal_user" invisible="1"/>
                <field name="th_type_ticket" invisible="1"/>
                <field name="th_help_provider_id" invisible="1" options="{'no_create': True, 'no_open': True}"/>
                <field name="th_support_area_id" options="{'no_create': True, 'no_open': True}" required="1"/>
                <field name="th_help_category" options="{'no_create': True, 'no_open': True}" required="1"/>
                <field name="th_category_characteristic_id" options="{'no_create': True, 'no_open': True}"
                       required="1"/>
                <field name="th_help_topic" options="{'no_create': True, 'no_open': True}" required="1"/>
                <field name="th_check_groups" invisible="1"/>
                <field name="th_is_deadline_assignees" invisible="1"/>
                <field name="team_id" position="move"/>
                <field name="th_user_id" attrs="{'readonly': [('id', '!=', False),('th_is_user_ticket', '=', False)]}" force_save="1" domain="th_user_id_domain"/>
                <field name="th_deadline_assignees"/>
                <field name="th_categ_of_tthl" invisible="1"/>
                <field name="th_type_errol" attrs="{'invisible': [('th_categ_of_tthl', '!=', True)],}"/>
                <field name="th_composite" attrs="{'invisible': [('th_categ_of_tthl', '!=', True)],}"/>
                <field name="th_major_id" attrs="{'invisible': [('th_categ_of_tthl', '!=', True)],}"
                       options="{'no_create': True, 'no_open': True}"/>
                <field name="th_user_production_id" options="{'no_create': True, 'no_open': True}"
                       attrs="{'invisible': [('th_categ_of_tthl', '!=', True)],}"/>
                <field name="th_check_user_assign_to" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='email_cc']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='ticket_type_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='properties']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='description']" position="replace">
                <notebook>
                    <page string="Description" name="description">
                        <field name="description" placeholder="Description of the ticket..."/>
                    </page>
                    <!--            <xpath expr="//page[@name='description']" position="after">-->
                    <field name="th_check_user_assign_to" invisible="1"/>
                    <page string="Corrective Actions" name="corrective_actions"
                          attrs="{'invisible': [('th_ticket_stage', '=', 'new')]}"
                          groups="helpdesk.group_helpdesk_user">
                        <field name="x_actions" attrs="{'readonly': [('th_check_user_assign_to', '=', False)]}"/>
                    </page>
                    <page string="Further suggestion" name="further_suggestion"
                          attrs="{'invisible': [('th_ticket_stage', '=', 'new')]}"
                          groups="helpdesk.group_helpdesk_user">
                        <field name="x_suggestion" attrs="{'readonly': [('th_check_user_assign_to', '=', False)]}"/>
                    </page>
                    <page string="Transfer" name="transfer" attrs="{'invisible': [('th_transferred_ids', '=', [])]}">
                        <field name="th_transferred_ids" readonly="1">
                            <tree>
                                <field name="th_transferor_user"/>
                                <field name="th_transfer_to_id"/>
                                <field name="th_transfer_date"/>
                                <field name="th_reason"/>
                            </tree>
                        </field>
                    </page>
                    <page string="Reason for Escalation" name="escalate"
                          attrs="{'invisible': [('th_escalate_ids', '=', [])]}">
                        <field name="th_escalate_ids" readonly="1">
                            <tree>
                                <field name="th_transferor_user"/>
                                <field name="th_transfer_to_id"/>
                                <field name="th_transfer_date"/>
                                <field name="th_reason"/>
                            </tree>
                            <form>
                                <group>
                                    <field name="th_transferor_user"/>
                                    <field name="th_transfer_to_id"/>
                                    <field name="th_transfer_date"/>
                                    <field name="th_reason"/>
                                </group>
                            </form>
                        </field>
                    </page>
                </notebook>
            </xpath>

        </field>
    </record>

    <record id="th_helpdesk_tickets_view_search" model="ir.ui.view">
        <field name="name">th_helpdesk_tickets_view_search</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_search"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name='filter_domain'>['|', ('name', 'ilike', self), ('id', 'ilike', self)]</attribute>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="attributes">
                <attribute name='invisible'>1</attribute>
            </xpath>
            <xpath expr="//field[@name='ticket_type_id']" position="attributes">
                <attribute name='invisible'>1</attribute>
            </xpath>
            <xpath expr="//field[@name='sla_ids']" position="attributes">
                <attribute name='invisible'>1</attribute>
            </xpath>
            <xpath expr="//filter[@name='sla_success']" position="before">
                <filter string="Rated Tickets" domain="[('x_ticket_rating', '!=', False)]" name="th_rated_ticket"/>
            </xpath>
            <xpath expr="//filter[@name='my_follow_ticket']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='unassigned']" position="replace">
                <filter string="Unassigned" domain="[('th_user_id','=',False)]" name="unassigned"/>
            </xpath>
            <xpath expr="//filter[@name='sla_success']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='sla_inprogress']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='sla_failed']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='message_needaction']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='ticket_type_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='tag_ids']" position="after">
                <field name="th_help_category"/>
            </xpath>
            <xpath expr="//filter[@name='urgent_priority']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='high_priority']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='medium_priority']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='low_priority']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='is_open']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='is_close']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='archive']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//filter[@name='my_ticket']" position="replace">
                <filter string="My Created Tickets" domain="[('create_uid','=',uid)]" name="my_created"/>
                <filter string="My Assigned Tickets" domain="['|', ('user_id','=',uid),('th_user_id','=',uid)]"
                        name="my_assigned"/>
            </xpath>

        </field>
    </record>

    <record id="helpdesk.helpdesk_ticket_action_main_my" model="ir.actions.act_window">
        <field name="context">{
            'search_default_my_ticket': True,
            'default_user_id': uid,
            }
        </field>
    </record>

    <record id="helpdesk.helpdesk_ticket_action_main_tree" model="ir.actions.act_window">
        <field name="context">{}</field>
    </record>

    <record id="aum_helpdesk_ticket_view_tree_action" model="ir.actions.act_window">
        <field name="name">Helpdesk Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="search_view_id" ref="helpdesk.helpdesk_tickets_view_search"/>
        <field name="context">{}</field>
    </record>

    <record id="helpdesk.helpdesk_ticket_menu_my" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <record id="helpdesk.helpdesk_menu_team_dashboard" model="ir.ui.menu">
        <field name="groups_id" eval="[(4, ref('helpdesk.group_helpdesk_manager'))]"/>
    </record>

    <record id="helpdesk.helpdesk_ticket_report_menu_main" model="ir.ui.menu">
        <field name="groups_id" eval="[(4, ref('group_helpdesk_care_manager'))]"/>
        <field name="action" ref="helpdesk.helpdesk_ticket_analysis_action"/>
    </record>

    <record id="helpdesk.helpdesk_tag_menu" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <record id="helpdesk.helpdesk_ticket_type_menu" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <record id="helpdesk.helpdesk_sla_menu_main" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <record id="helpdesk.helpdesk_menu_config_activity_type" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <record id="helpdesk.helpdesk_ticket_menu_main" model="ir.ui.menu">
        <field name="action" ref="aum_helpdesk_ticket_view_tree_action"/>
    </record>

    <record id="helpdesk.helpdesk_ticket_menu_all" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <record id="helpdesk.helpdesk_ticket_report_menu_ratings" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <record id="helpdesk.helpdesk_ticket_report_menu" model="ir.ui.menu">
        <field name="active" eval="True"/>
    </record>

    <record id="helpdesk.helpdesk_ticket_report_menu_sla_analysis" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <record id="helpdesk.helpdesk_ticket_analysis_action" model="ir.actions.act_window">
        <field name="context">{'search_default_x_ticket_rating': 1, 'graph_mode': 'pie'}</field>
    </record>

    <record id="aum_helpdesk_ticket_view_kanban" model="ir.ui.view">
        <field name="name">aum.helpdesk.ticket.view.kanban</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_kanban"/>
        <field name="arch" type="xml">

            <xpath expr="//kanban" position="attributes">
                <attribute name="records_draggable">0</attribute>
            </xpath>
            <xpath expr="//div[@class='oe_kanban_bottom_right']" position="attributes">
                <attribute name="style">display: none;</attribute>
            </xpath>

        </field>
    </record>

    <record id="th_schedule_escalate_sla_th" model="ir.cron">
        <field name="name">Schedule escalate SLA</field>
        <field name="interval_number">1</field>
        <field name="nextcall" eval="(datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d 19:00:00')"/>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active">1</field>
        <field name="doall">0</field>
        <field name="model_id" ref="helpdesk.model_helpdesk_ticket"/>
        <field name="state">code</field>
        <field name="code">
            model.sudo().search([])._schedule_escalate_sla_th()
        </field>
    </record>

    <record id="helpdesk.helpdesk_team_menu" model="ir.ui.menu">
        <field name="groups_id"
               eval="[(6, 0, [ref('helpdesk.group_helpdesk_manager'), ref('th_helpdesk.group_helpdesk_care_manager')])]"/>
        <field name="parent_id" ref="helpdesk.menu_helpdesk_root"/>
        <field name="sequence" eval="19"/>
    </record>

</odoo>
