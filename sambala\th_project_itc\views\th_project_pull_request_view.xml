<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="th_project_pull_request_action" model="ir.actions.act_window">
        <field name="name">Pull request</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.project.pull.request</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="th_project_pull_request_view_tree" model="ir.ui.view">
        <field name="name">th_project_pull_request_view_tree</field>
        <field name="model">th.project.pull.request</field>
        <field name="arch" type="xml">
            <tree string="project_pull_request_tree">
                <field name="name"/>
                <field name="th_tags_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="th_link_pr"/>
                <field name="th_id_pull_request"/>
                <field name="th_task_id"/>
                <field name="state"/>
                <field name="create_uid" string="Ngư<PERSON>i tạo Pull Request"/>
                <field name="create_date" string="Ngày tạo"/>
            </tree>
        </field>
    </record>

    <record id="th_task_pr_action" model="ir.actions.act_window">
            <field name="name">Nhiệm vụ</field>
            <field name="res_model">th.project.task</field>
            <field name="domain">[('pr_ids','=', active_id)]</field>
            <field name="view_mode">tree,form</field>
     </record>

    <record id="th_project_pull_request_view_form" model="ir.ui.view">
        <field name="name">th_project_pull_request_view_form</field>
        <field name="model">th.project.pull.request</field>
        <field name="arch" type="xml">
            <form string="project_pull_request_form">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="samdev,uat,staging,product" options="{'clickable': 1}" attrs="{'invisible': [('state','=','staging')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="samdev,uat,staging,product" attrs="{'invisible': [('state','in',['samdev','uat','product'])]}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                            <button name="th_action_open_task"
                                    type="object"
                                    class="oe_stat_button"
                                    icon="fa-archive">
                                Nhiệm vụ
                            </button>
                            <button name="th_action_open_plan"
                                    type="object"
                                    class="oe_stat_button"
                                    icon="fa-archive"
                                    attrs="{'invisible': [('state','!=','staging')]}">
                                Kế hoạch
                            </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" attrs="{'readonly': [('state','in',['uat','staging','product'])]}"/>
                            <field name="th_link_pr" attrs="{'readonly': [('state','in',['uat','staging','product'])]}"/>
                            <field name="th_id_pull_request"/>
                        </group>
                        <group>
                            <field name="th_tags_ids" widget="many2many_tags"
                                   options="{'color_field': 'color'}"
                                   attrs="{'readonly': [('state','in',['uat','staging','product'])]}"/>
                            <field name="th_task_id" attrs="{'readonly': [('state','in',['uat','staging','product'])]}"/>
                            <field name="create_uid" string="Người tạo Pull Request" attrs="{'readonly': 1}"/>
                            <field name="create_date" string="Ngày tạo"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description"/>
                        </page>
                    </notebook>
                </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
            </form>
        </field>
    </record>
    
    <menuitem id="th_menu_pull_request" name="Pull Request" parent="th_menu_project_manager" action="th_project_pull_request_action"/>

    
</odoo>
