<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
<!--    <record id="category_user_of_hl_root" model="ir.module.category">-->
<!--        <field name="name"><PERSON><PERSON><PERSON> li<PERSON></field>-->
<!--        <field name="description">Nhóm quyền của trung tâm học liệu</field>-->
<!--        <field name="sequence">1</field>-->
<!--    </record>-->

<!--    <record id="category_user_of_tthl" model="ir.module.category">-->
<!--        <field name="name"><PERSON><PERSON><PERSON> liệu</field>-->
<!--        <field name="description">Nhóm quyền của trung tâm học liệu</field>-->
<!--        <field name="parent_id" ref="category_user_of_hl_root"/>-->
<!--        <field name="sequence">20</field>-->
<!--    </record>-->

    <record id="th_user_handler_group_of_hl" model="res.groups">
        <field name="name">Nhân viên xử lý lỗi HL</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_template_user_base')),
            (4, ref('th_trm.group_trm_user')),
            (4, ref('th_lpm.group_lpm_user')),
            (4, ref('th_feedback.group_feedback_handler')),
            ]"/>
    </record>

    <record id="th_user_classifier_group_of_hl" model="res.groups">
        <field name="name">Nhân viên phân loại lỗi HL</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_user_handler_group_of_hl')),
            (4, ref('th_trm.group_trm_user')),
            (4, ref('th_lpm.group_lpm_user')),
            (4, ref('th_feedback.group_feedback_classifier')),
            ]"/>
    </record>

    <record id="th_leader_group_of_tthl" model="res.groups">
        <field name="name">Trưởng nhóm HL</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_user_classifier_group_of_hl')),
            (4, ref('th_trm.group_trm_manager')),
            (4, ref('th_lpm.group_lpm_leader')),
            (4, ref('hr_attendance.group_hr_attendance_user')),
            (4, ref('th_feedback.group_feedback_category_manager')),
            ]"/>
    </record>
        <record id="th_head_leader_group_of_tthl" model="res.groups">
        <field name="name">Trưởng phòng HL</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_leader_group_of_tthl')),
            (4, ref('th_trm.group_trm_supervisor')),
            ]"/>
    </record>
</data>
<!--    Chú thích các nhóm quyền-->
<!--    group_trm_user: TRM:Nhân viên-->
<!--    group_lpm_user: LPM:Nhân viên-->

<!--    group_trm_manager: TRM:Trưởng nhóm-->
<!--    group_lpm_leader: LPM: Trưởng nhóm-->
<!--    group_hr_user: Nhân viên: Cán bộ-->
<!--    group_hr_attendance_user: Chấm công: -->

<!--    <record id="th_admin_group_of_tthl" model="res.groups">-->
<!--        <field name="name">Quản trị viên</field>-->
<!--        <field name="category_id" ref="category_user_of_tthl"/>-->
<!--        <field name="implied_ids" eval="[-->
<!--            (4, ref('th_leader_group_of_tthl')),-->
<!--            ]"/>-->
<!--    </record>-->

</odoo>