<?xml version="1.0" encoding="utf-8"?>
<odoo>
<!--    <record id="th_category_role_operation_root" model="ir.module.category">-->
<!--        <field name="name">V<PERSON><PERSON> hành</field>-->
<!--        <field name="description"><PERSON><PERSON> quyền quản lý Trung tâm Vận hành</field>-->
<!--        <field name="sequence">1</field>-->
<!--    </record>-->

<!--    <record id="th_category_role_operation" model="ir.module.category">-->
<!--        <field name="name">VH</field>-->
<!--        <field name="description">Phân quyền quản lý Trung tâm Vận hành</field>-->
<!--        <field name="parent_id" ref="th_category_role_operation_root"/>-->
<!--        <field name="sequence">15</field>-->
<!--    </record>-->

    <!-- <PERSON><PERSON> tr<PERSON>: <PERSON>uyền N<PERSON>ân viên Vận hành -->
    <record id="th_role_operation_user" model="res.groups">
        <field name="name">Nhân viên VH</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_template_user_base')),
            (4, ref('th_crm.th_group_user_crm')),
            (4, ref('th_apm.group_apm_user')),
            (4, ref('th_srm.group_srm_user')),
            (4, ref('th_crm.th_group_crm_tvts')),
            (4, ref('th_apm.group_apm_after_order')),
            (4, ref('th_apm.group_leader_apm_after_order'))
        ]"/>
    </record>

    <!-- Vai trò: Quyền Trưởng phòng Vận hành -->
    <record id="th_role_operation_manager" model="res.groups">
        <field name="name">Trưởng nhóm VH</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_role_operation_user')),
            (4, ref('th_crm.th_group_leader_crm')),
            (4, ref('th_apm.group_apm_leader')),
            (4, ref('th_srm.group_srm_manager')),
            (4, ref('hr_attendance.group_hr_attendance_user')),
        ]"/>
    </record>

    <!-- Vai trò: Quyền Admin Vận hành -->
<!--    <record id="th_role_operation_admin" model="res.groups">-->
<!--        <field name="name">Admin</field>-->
<!--        <field name="category_id" ref="th_category_role_operation"/>-->
<!--        <field name="implied_ids" eval="[-->
<!--            (4, ref('th_role_operation_manager')),-->
<!--            (4, ref('th_crm.th_group_admin_crm')),-->
<!--            (4, ref('th_apm.group_apm_administrator')),-->
<!--            (4, ref('th_srm.group_srm_admin')),-->
<!--        ]"/>-->
<!--    </record>-->


</odoo>
