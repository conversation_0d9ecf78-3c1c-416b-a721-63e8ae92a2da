from odoo import models, api

class ApmSaleOrder(models.Model):
    _name = "sale.order"
    _inherit = ["sale.order", "th.intermediate.table"]

    def get_data_sync(self, rec, action, th_type_sync='an_hour'):
        data = {
            'th_apm_id': rec.th_apm_id.id if rec.th_apm_id else False,
            'name': rec.name if rec.name else False,
            'partner_id': rec.partner_id.id if rec.partner_id else False,
            'th_apm_source_group_id': rec.th_apm_source_group_id.id if rec.th_apm_source_group_id else False,
            'th_channel_id': rec.th_channel_id.id if rec.th_channel_id else False,
            'th_total_received_excessive': rec.th_total_received_excessive if rec.th_total_received_excessive else False,
            'date_order': rec.date_order.isoformat() if rec.date_order else False,
            'pricelist_id': rec.pricelist_id.id if rec.pricelist_id else False,
            'payment_term_id': rec.payment_term_id.id if rec.payment_term_id else False,
            'th_introducer_id': rec.th_introducer_id.id if rec.th_introducer_id else False,
            'partner_invoice_id': rec.partner_invoice_id.id if rec.partner_invoice_id else False,
            'partner_shipping_id': rec.partner_shipping_id.id if rec.partner_shipping_id else False,
            'th_status': rec.th_status if rec.th_status else False,
            'state': rec.state if rec.state else False,
            'th_identifier_code': rec.th_identifier_code if rec.th_identifier_code else False,
            # 'th_partner_birthday': rec.th_partner_birthday if rec.th_partner_birthday else False,
            'th_srm_class': rec.th_srm_class if rec.th_srm_class else False,
            'th_srm_specialization_class': rec.th_srm_specialization_class if rec.th_srm_specialization_class else False,
            'th_srm_major': rec.th_srm_major.id if rec.th_srm_major else False,
            'th_srm_school': rec.th_srm_school.id if rec.th_srm_school else False,
            'th_srm_lead_id': rec.th_srm_lead_id.id if rec.th_srm_lead_id else False,
            'th_sale_order': rec.th_sale_order if rec.th_sale_order else False,
            'th_status_payment_invoiced': rec.th_status_payment_invoiced if rec.th_status_payment_invoiced else False,
        }
        data_send = data.copy()
        data['order_lines'] = []
        data_send['order_lines'] = []
        if rec.order_line:
            for order_l in rec.order_line:
                order_l_data = {
                    'product_template_id': order_l.product_template_id.id,
                    'product_id': order_l.product_id.id,
                    'product_uom_qty': order_l.product_uom_qty,
                    'discount': order_l.discount,
                    'price_unit': order_l.price_unit,
                }
                data['order_lines'].append(order_l_data)

                order_l_data_send = {
                    'product_id': self.sudo()._find_external_id(
                        th_internal_id=order_l.product_id.id,
                        th_model_name='product.product'
                    ) if order_l.id else False,
                    'product_template_id': self.sudo()._find_external_id(
                        th_internal_id=order_l.product_template_id.id,
                        th_model_name='product.template'
                    ) if order_l.id else False,
                    'product_uom_qty': order_l.product_uom_qty,
                    'discount': order_l.discount,
                    'price_unit': order_l.price_unit,
                }
                data_send['order_lines'].append(order_l_data_send)

        data_send.update({
            'th_apm_id': self.sudo()._find_external_id(th_internal_id=rec.th_apm_id.id, th_model_name='th.apm') if rec.th_apm_id else False,
            'partner_id': self.sudo()._find_external_id(th_internal_id=rec.partner_id.id, th_model_name='res.partner') if rec.partner_id else False,
            'th_apm_source_group_id': self.sudo()._find_external_id(th_internal_id=rec.th_apm_source_group_id.id, th_model_name='th.source.group') if rec.th_apm_source_group_id else False,
            'th_channel_id':  self.sudo()._find_external_id(th_internal_id=rec.th_channel_id.id, th_model_name='th.info.channel') if rec.th_channel_id else False,
            'pricelist_id': 1,
            'payment_term_id': self.sudo()._find_external_id(th_internal_id=rec.payment_term_id.id, th_model_name='account.payment.term') if rec.payment_term_id else False,
            'th_introducer_id': self.sudo()._find_external_id(th_internal_id=rec.th_introducer_id.id, th_model_name='res.partner') if rec.th_introducer_id else False,
            'partner_invoice_id': self.sudo()._find_external_id(th_internal_id=rec.partner_invoice_id.id, th_model_name='res.partner') if rec.partner_invoice_id else False,
            'partner_shipping_id': self.sudo()._find_external_id(th_internal_id=rec.partner_shipping_id.id, th_model_name='res.partner') if rec.partner_shipping_id else False,
            'th_srm_major': self.sudo()._find_external_id(th_internal_id=rec.th_srm_major.id, th_model_name='th.major') if rec.th_srm_major else False,
            'th_srm_school': self.sudo()._find_external_id(th_internal_id=rec.th_srm_school.id, th_model_name='th.origin') if rec.th_srm_school else False,
            'th_srm_lead_id':  self.sudo()._find_external_id(th_internal_id=rec.th_srm_lead_id.id, th_model_name='th.student') if rec.th_srm_lead_id else False,
        })
        return data, data_send

    @api.model
    def th_trigger_sale_order(self, records, action, th_type_sync='an_hour'):
        for rec in records:
            mapping = self.env['th.mapping.id'].sudo().search([('th_internal_id', '=', rec.id), ('th_model_name', '=', 'sale.order')], limit=1)
            if rec.th_complete_payment and rec.th_sale_order!='crm' or mapping:
                old_values = rec._context.get('old_values', {}).values()
                if old_values and mapping:
                    th_data= self.optimal_data(rec, list(old_values))
                    val = {
                        'name': rec.name,
                        'th_type_sync': th_type_sync if rec.th_sale_order == 'apm' else 'a_day',
                        'th_internal_id': rec.id,
                        'th_data': th_data,
                        'th_system': 'b2b',
                    }
                    if action == 'create_or_update':
                        val['th_data_send'] = th_data[0]
                        self.sudo().th_func_create_or_update(val)
                else:
                    th_data, th_data_send = self.get_data_sync(rec, action, th_type_sync)
                    val = {
                        'name': rec.name,
                        'th_type_sync': th_type_sync if rec.th_sale_order == 'apm' else 'a_day',
                        'th_internal_id': rec.id,
                        'th_data': th_data,
                        'th_system': 'b2b',
                    }
                    if action == 'create_or_update':
                        val['th_data_send'] = th_data_send
                        self.sudo().th_func_create_or_update(val)
                    elif action == 'delete':
                        self.sudo().th_func_delete(val)