<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!--    <record id="th_category_role_admin_root" model="ir.module.category">-->
<!--        <field name="name">Quản trị viên</field>-->
<!--        <field name="description"><PERSON><PERSON><PERSON><PERSON> quyền của quản trị viên toàn hệ thống</field>-->
<!--        <field name="sequence">1</field>-->
<!--    </record>-->

<!--    <record id="th_category_role_admin" model="ir.module.category">-->
<!--        <field name="name">Admin</field>-->
<!--        <field name="description">Nhóm quyền của quản trị viên toàn hệ thống</field>-->
<!--        <field name="parent_id" ref="th_category_role_admin_root"/>-->
<!--        <field name="sequence">10</field>-->
<!--    </record>-->

    <record id="th_role_sambala_api_user" model="res.groups">
        <field name="name">Sambala API</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_template_user_base')),
            (4, ref('th_crm.th_group_admin_crm')),
            (4, ref('th_apm.group_apm_administrator')),
            (4, ref('th_srm.group_srm_admin')),
            (4, ref('th_trm.group_trm_admin')),
            (4, ref('th_lpm.group_lpm_administrator')),
            (4, ref('th_prm.group_prm_administrator')),
            (4, ref('sales_team.group_sale_manager')),
            (4, ref('planning.group_planning_manager')),
            (4, ref('project.group_project_manager')),
            (4, ref('hr_timesheet.group_hr_timesheet_approver')),
            (4, ref('helpdesk.group_helpdesk_manager')),
            (4, ref('sign.group_sign_manager')),
            (4, ref('documents.group_documents_manager')),
            (4, ref('hr_attendance.group_hr_attendance_manager')),
            (4, ref('maintenance.group_equipment_manager')),
            (4, ref('approvals.group_approval_manager')),
            (4, ref('fastapi.group_fastapi_manager')),
            (4, ref('hr.group_hr_manager')),
            (4, ref('hr_holidays.group_hr_holidays_manager')),
            (4, ref('hr_contract.group_hr_contract_manager')),
            (4, ref('hr_payroll.group_hr_payroll_manager')),
            (4, ref('th_project_itc.th_group_project_manager')),
            (4, ref('th_srm_b2b.th_srm_b2b_partner_group')),
            (4, ref('th_apm_b2b.th_apm_group_partner_manager')),
            (4, ref('th_crm_b2b.th_crm_b2b_partner_group')),
            (4, ref('account.group_account_manager')),
            (4, ref('hr_appraisal.group_hr_appraisal_manager')),
            (4, ref('maintenance.group_equipment_manager')),
            (4, ref('th_feedback.group_feedback_admin')),
            (4, ref('survey.group_survey_manager')),
            (4, ref('purchase.group_purchase_manager')),
            (4, ref('website_slides.group_website_slides_manager')),
            (4, ref('website.group_website_designer')),
            (4, ref('base.group_partner_manager')),
            (4, ref('base.group_erp_manager')),
        ]"/>
    </record>

    <record id="th_role_admin_user" model="res.groups">
        <field name="name">Admin</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_role_sambala_api_user')),
            (4, ref('base.group_system')),
        ]"/>
    </record>

<!--    Chú thích các nhóm quyền -->
<!--CRM	Admin	th_crm.th_group_admin_crm-->
<!--APM	Admin	th_apm.group_apm_administrator-->
<!--SRM	Admin	th_srm.group_srm_admin-->
<!--TRM	Admin	th_trm.group_trm_admin-->
<!--LPM	Admin	th_lpm.group_lpm_administrator-->
<!--PRM	Admin	th_prm.group_prm_administrator-->
<!--Bán hàng	Admin	sales_team.group_sale_manager-->
<!--Kế hoạch	Admin	planning.group_planning_manager-->
<!--Dự Án	Admin	project.group_project_manager-->
<!--Thời gian biểu	Người dùng tất cả bảng chấm công	hr_timesheet.group_hr_timesheet_approver-->
<!--Hỗ Trợ	Admin	helpdesk.group_helpdesk_manager-->
<!--Ký	Admin	sign.group_sign_manager-->
<!--Tài liệu	Admin	documents.group_documents_manager-->
<!--Chấm Công	Admin	hr_attendance.group_hr_attendance_manager-->
<!--Bảo Trì	Quản lý thiết bị	maintenance.group_equipment_manager-->
<!--Phê duyệt	Admin	approvals.group_approval_manager-->
<!--Nhân viên	Admin	hr.group_hr_manager-->
<!--Ngày nghỉ	Admin	hr_holidays.group_hr_holidays_manager-->
<!--Hợp đồng	Admin	hr_contract.group_hr_contract_manager-->
<!--Bảng lương	Hành chính nhân sự	hr_payroll.group_hr_payroll_manager-->
<!--ITC	Admin	th_project_itc.th_group_project_manager-->
<!--SRM Đối tác	Đối tác	th_srm_b2b.th_srm_b2b_partner_group-->
<!--APM Đối tác	Đối tác	th_apm_b2b.th_apm_group_partner_manager-->
<!--CRM Đối tác	Đối tác	th_crm_b2b.th_crm_b2b_partner_group-->
<!--Kế toán	Quản trị viên thanh toán	account.group_account_manager-->
<!--Quản trị	Thiết lập	base.group_system-->
<!--Fast API	tích quyền Admin	fastapi.group_fastapi_manager-->
<!--đánh giá	Admin	hr_appraisal.group_hr_appraisal_manager-->
<!--Bảo trì (QLTS)	Quản lý thiết bị	maintenance.group_equipment_manager-->
<!--Quản lý Feedback	Admin	th_feedback.group_feedback_admin-->
<!--tuyển dụng	Admin	hr_recruitment.group_hr_recruitment_manage-->
<!--User audit log	Tích quyền	th_user_audit_log.th_group_user_audit_log_view-->
<!--Job Queue	Job Queue manager	queue_job.group_queue_job_manager-->
<!--Khảo sát	Admin	survey.group_survey_manager-->
<!--mua hàng	Admin	purchase.group_purchase_manager-->
<!--eLearning	Người quản lý	website_slides.group_website_slides_manager-->
<!--Trang web	Biên tập và thiết kế	website.group_website_designer-->
<!--Tạo liên hệ	Tích quyền	base.group_partner_manager-->

</odoo>
